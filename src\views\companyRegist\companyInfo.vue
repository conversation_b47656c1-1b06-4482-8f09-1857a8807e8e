<template>
  <div class="app-container">
    <el-container :style="{ height: '100%' }">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item title="入驻申请填报" :name="1">
            <el-alert
              v-if="
                action !== 'companyInfoAddApi' &&
                action !== 'audit' &&
                action !== 'view'
              "
              :title="title"
              :type="alertType"
              center
              :description="description"
              show-icon
            />
            <el-form
              ref="basic"
              label-position="top"
              :model="basicFormModel"
              :rules="basicFormRules"
              :inline="true"
            >
              <el-row>
                <el-form-item prop="role" label="入驻角色：" style="width: 49%">
                  <el-select
                    v-model="basicFormModel.role"
                    placeholder="请选择入驻角色"
                    multiple
                    :disabled="disabled"
                  >
                    <el-option
                      v-for="item in entRoleList"
                      :key="item.value"
                      v-bind="item"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  :prop="
                    action === 'audit' || action === 'view' ? 'entPrefix' : ''
                  "
                  label="企业前缀："
                  style="width: 49%"
                >
                  <div style="display: flex; align-items: center">
                    <div>88.588.</div>
                    <el-input
                      v-model="basicFormModel.entPrefix"
                      maxlength="20"
                      placeholder="请输入企业前缀"
                      :disabled="disabled && action !== 'audit'"
                      @input="handleEntPrefixInput"
                      @keypress="handleEntPrefixKeypress"
                    />
                  </div>
                </el-form-item>
              </el-row>
              <el-form-item
                prop="subjectClassification"
                label="主体分类："
                style="width: 49%"
              >
                <el-select
                  v-model="basicFormModel.subjectClassification"
                  placeholder="请选择主体分类"
                  :disabled="disabled"
                >
                  <el-option
                    v-for="item in enterpriseDominantList"
                    :key="item.value"
                    v-bind="item"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                prop="industry"
                label="企业/机构行业（行业大类 — 行业小类）："
                style="width: 49%"
              >
                <el-cascader
                  v-model="basicFormModel.industry"
                  placeholder="请选择行业大类 — 行业小类"
                  :options="workTypeList"
                  :props="cascaderProps"
                  :disabled="disabled"
                />
              </el-form-item>
              <el-row>
                <el-form-item
                  prop="businessLicense"
                  label="营业执照："
                  class="el-form-item-width"
                >
                  <upload-img
                    :limit-count="1"
                    :img-list.sync="businessLicenseList"
                    tip-position="right"
                    :readonly="disabled"
                  >
                    <template v-if="!disabled" #tip>
                      <div style="margin-top: 12px">
                        只能上传jpg/png文件，且不超过4M
                      </div>
                      <div style="display: flex; gap: 10px">
                        <el-button type="primary" @click="saveDetail(false)"
                          >自动识别</el-button
                        >
                        <div style="line-height: 1.2">
                          营业执照相关信息可上传执照图片自动识别<br />自动识别信息可能有误差，请仔细核验
                        </div>
                      </div>
                    </template>
                  </upload-img>
                </el-form-item>
              </el-row>

              <el-form-item
                prop="companyType"
                label="企业类型："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.companyType"
                  placeholder="请输入企业类型"
                  :disabled="disabled"
                />
              </el-form-item>

              <el-form-item
                prop="companyName"
                label="企业/机构名称："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.companyName"
                  placeholder="请输入企业/机构名称"
                  :disabled="disabled"
                />
              </el-form-item>

              <el-form-item
                prop="registeredAddress"
                label="注册所在地："
                class="el-form-item-width"
              >
                <el-cascader
                  v-model="basicFormModel.registeredAddress"
                  placeholder="请输入注册所在地"
                  :options="areaList"
                  :props="cascaderProps"
                  :disabled="disabled"
                />
              </el-form-item>

              <el-form-item
                prop="entCode"
                label="统一社会信用代码："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.entCode"
                  placeholder="请输入统一社会信用代码"
                  :disabled="disabled"
                />
              </el-form-item>

              <el-form-item
                prop="detailedAddress"
                label="详细地址："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.detailedAddress"
                  placeholder="请输入详细地址"
                  :disabled="disabled"
                />
              </el-form-item>

              <el-form-item
                prop="registeredCapital"
                label="注册资本："
                class="el-form-item-width"
              >
                <el-input-number
                  v-model="basicFormModel.registeredCapital"
                  placeholder="请选择注册资本"
                  :disabled="disabled"
                />
              </el-form-item>

              <el-form-item
                prop="establishmentDate"
                label="成立日期："
                class="el-form-item-width"
              >
                <el-date-picker
                  v-model="basicFormModel.establishmentDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                  placeholder="选择成立日期"
                  :disabled="disabled"
                />
              </el-form-item>

              <el-form-item
                prop="businessEndDate"
                label="营业期限至："
                class="el-form-item-width"
              >
                <el-date-picker
                  v-model="basicFormModel.businessEndDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  style="width: 60%; margin-right: 10px"
                  placeholder="选择营业期限至"
                  @change="checked = false"
                  :disabled="disabled"
                />
                <el-checkbox
                  v-model="checked"
                  :disabled="disabled"
                  @change="basicFormModel.businessEndDate = undefined"
                  >无明确期限</el-checkbox
                >
              </el-form-item>

              <el-form-item
                prop="registrationAuthority"
                label="登记机关："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.registrationAuthority"
                  placeholder="请输入登记机关"
                  :disabled="disabled"
                />
              </el-form-item>

              <el-row>
                <el-form-item
                  prop="businessScope"
                  label="经营范围："
                  style="width: 98%"
                >
                  <el-input
                    v-model="basicFormModel.businessScope"
                    placeholder="请输入经营范围"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-row>
              <el-form-item
                prop="legalPersonName"
                label="法人姓名："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.legalPersonName"
                  placeholder="请输入法人姓名"
                  :disabled="disabled"
                />
              </el-form-item>

              <el-form-item
                prop="legalPersonIdType"
                label="法人证件类型："
                class="el-form-item-width"
              >
                <el-select
                  v-model="basicFormModel.legalPersonIdType"
                  placeholder="请选择法人证件类型"
                  :disabled="disabled"
                >
                  <el-option
                    v-for="item in cardList"
                    :key="item.value"
                    v-bind="item"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                prop="legalPersonIdNumber"
                label="法人证件号码："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.legalPersonIdNumber"
                  placeholder="请输入法人证件号码"
                  :disabled="disabled"
                />
              </el-form-item>
              <el-row>
                <el-form-item
                  prop="idCardPhotoUrl"
                  label="证件照片（正反面）："
                  class="el-form-item-width"
                >
                  <div style="display: flex">
                    <upload-img
                      :limit-count="1"
                      :img-list.sync="frontPicList"
                      tip-position="right"
                      :readonly="disabled"
                    >
                      <template #tip><span /></template>
                    </upload-img>
                    <upload-img
                      :limit-count="1"
                      :img-list.sync="reversePicList"
                      tip-position="right"
                      :readonly="disabled"
                    >
                      <template v-if="!disabled" #tip>
                        <div style="margin-top: 35px">
                          只能上传jpg/png文件，且不超过4M
                        </div>
                      </template>
                    </upload-img>
                  </div>
                </el-form-item>
              </el-row>

              <el-form-item
                prop="contactPersonName"
                label="联系人姓名："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.contactPersonName"
                  placeholder="请输入联系人姓名"
                  :disabled="disabled"
                />
              </el-form-item>

              <el-form-item
                prop="contactPersonPhone"
                label="联系人手机："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.contactPersonPhone"
                  placeholder="请输入联系人手机"
                  :disabled="disabled"
                />
              </el-form-item>

              <el-form-item
                prop="contactPersonEmail"
                label="联系人邮箱："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.contactPersonEmail"
                  placeholder="请输入联系人邮箱"
                  :disabled="disabled"
                />
              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item
            v-if="action === 'audit' || action === 'view'"
            title="审核意见"
            :name="2"
          >
            <el-form ref="form" :model="form">
              <el-form-item
                prop="auditMessage"
                :rules="auditRules"
                inline-message
                show-message
              >
                <el-input
                  v-model="form.auditMessage"
                  clearable
                  :disabled="action === 'view'"
                  type="textarea"
                  :maxlength="200"
                  :rows="5"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-form>
            <div ref="targetElement" />
          </el-collapse-item>
        </el-collapse>
      </el-main>
      <el-footer class="button-container">
        <div v-if="action !== 'success'">
          <el-button
            v-if="action === 'companyInfoEditApi'"
            @click="handleChange"
            >{{ disabled ? "修改填报" : "取消修改" }}</el-button
          >
          <el-button
            v-if="
              (action === 'companyInfoAddApi' ||
                action === 'companyInfoEditApi') &&
              !disabled
            "
            type="primary"
            @click="addAndEditFrom"
            >保存</el-button
          >
          <el-button
            v-if="action === 'companyInfoEditApi' && disabled"
            type="primary"
            @click="submit(1)"
            >提交审核</el-button
          >
          <el-button v-if="action === 'back'" type="danger" @click="backup"
            >撤销</el-button
          >
        </div>
        <!--        审核按钮-->
        <el-button
          v-if="action === 'audit' || action === 'view'"
          @click="returnBack"
          >返回</el-button
        >
        <el-button v-if="action === 'audit'" type="primary" @click="auditYes"
          >审核通过</el-button
        >
        <el-button v-if="action === 'audit'" type="danger" @click="auditNo"
          >审核驳回</el-button
        >
      </el-footer>
    </el-container>
    <simple-data-dialog
      v-if="imgDialogVisible"
      title="添加产品图片"
      :visible="true"
      size="small"
    >
      <el-form
        ref="imgForm"
        label-position="top"
        :model="imgFormModel"
        :rules="imgFormRules"
        :inline="true"
      >
        <el-form-item
          prop="fileList"
          label="产品图片："
          class="el-form-item-width"
        >
          <upload-img :limit-count="1" :img-list.sync="imgFormModel.fileList" />
        </el-form-item>
        <el-form-item prop="remark" label="说明：" style="width: 100%">
          <el-input
            v-model="imgFormModel.remark"
            type="textarea"
            placeholder="请输入图片说明"
            maxlength="200"
          />
        </el-form-item>
      </el-form>
      <el-footer class="button-container">
        <el-button @click="imgDialogVisible = false">取消</el-button>
      </el-footer>
    </simple-data-dialog>
  </div>
</template>
<script>
import UploadImg from "@/components/DataDialog/uploadImg";
import SimpleDataDialog from "@/components/SimpleDataDialog/index.vue";
import accApi from "@/api/acc/acc";
import acc from "@/api/acc/acc";

export default {
  name: "ProductDetail",
  components: {
    SimpleDataDialog,
    UploadImg,
  },
  data() {
    // 自定义校验规则
    const validateBusinessEndDate = (rule, value, callback) => {
      if (!value && !this.checked) {
        callback(new Error("请选择营业期限至或勾选无明确期限"));
      } else {
        callback();
      }
    };
    return {
      action: null,
      auditMessage: null,
      categoryList: [],
      workTypeList: [],
      // 入驻角色
      entRoleList: [],
      enterpriseDominantList: [],
      cardList: [],
      areaList: [],
      dialogVisible: false,
      disabled: false,
      checked: false,
      imgDialogVisible: false,
      collapse: [1, 2, 3],
      imgFormModel: {
        fileList: [],
        remark: "",
      },
      form: {
        auditMessage: "", // 输入框绑定的值
      },
      auditRules: [
        { required: true, message: "驳回时审核意见不能为空", trigger: "blur" },
      ],
      imgFormRules: {
        fileList: { required: true, message: "请上传图片" },
        remark: { required: true, message: "请输入图片说明" },
      },
      basicFormModel: {
        role: [],
        businessLicense: undefined,
        idCardPhotoUrl: undefined,
      },
      businessLicenseList: [],
      frontPicList: [],
      reversePicList: [],
      cascaderProps: {
        expandTrigger: "hover",
      },
      basicFormRules: {
        role: [
          {
            required: true,
            message: "请输入入驻角色",
            trigger: "change",
          },
        ],
        businessEndDate: [
          {
            required: true,
            validator: validateBusinessEndDate,
            trigger: ["blur", "change"],
          },
        ],
        companyType: [
          { required: true, message: "请输入企业类型", trigger: "blur" },
        ],
        subjectClassification: [
          { required: true, message: "请选择主体分类", trigger: "change" },
        ],
        entPrefix: [
          { required: true, message: "请输入企业前缀", trigger: "blur" },
          {
            pattern: /^[0-9]\d*$/,
            message: "企业前缀只能输入正整数",
            trigger: "blur",
          },
        ],
        industry: [
          {
            required: true,
            message: "请选择行业大类 — 行业小类",
            trigger: "change",
          },
        ],
        companyName: [
          { required: true, message: "请输入企业/机构名称", trigger: "blur" },
        ],
        registeredAddress: [
          { required: true, message: "请选择注册所在地", trigger: "change" },
        ],
        entCode: [
          {
            required: true,
            message: "请输入统一社会信用代码",
            trigger: "blur",
          },
          {
            pattern: /^[0-9A-Z]{18}$/,
            message: "统一社会信用代码只能包含字母和数字",
            trigger: "blur",
          },
        ],
        detailedAddress: [
          { required: true, message: "请输入详细地址", trigger: "blur" },
        ],
        registeredCapital: [
          { required: true, message: "请输入注册资本", trigger: "blur" },
        ],
        establishmentDate: [
          { required: true, message: "请选择成立日期", trigger: "change" },
        ],
        registrationAuthority: [
          { required: true, message: "请输入登记机关", trigger: "blur" },
        ],
        businessScope: [
          { required: true, message: "请输入经营范围", trigger: "blur" },
        ],
        legalPersonName: [
          { required: true, message: "请输入法人姓名", trigger: "blur" },
        ],
        legalPersonIdType: [
          { required: true, message: "请选择法人证件类型", trigger: "change" },
        ],
        legalPersonIdNumber: [
          { required: true, message: "请输入法人证件号码", trigger: "blur" },
        ],
        contactPersonName: [
          { required: true, message: "请输入联系人姓名", trigger: "blur" },
        ],
        contactPersonPhone: [
          { required: true, message: "请输入联系人手机", trigger: "blur" },
          {
            pattern: /^1[3456789]\d{9}$/,
            message: "请输入有效的手机号码",
            trigger: "blur",
          },
        ],
        contactPersonEmail: [
          { required: true, message: "请输入联系人邮箱", trigger: "blur" },
          { type: "email", message: "请输入有效的邮箱地址", trigger: "blur" },
        ],
        businessLicense: [
          { required: true, message: "请上传营业执照", trigger: "change" },
        ],
        idCardPhotoUrl: [
          {
            required: true,
            message: "请上传证件照片正反面",
            trigger: "change",
          },
        ],
      },
      alertType: "",
      description: "",
      title: "",
    };
  },
  computed: {},
  watch: {
    businessLicenseList: {
      handler(val) {
        if (val.length > 0) {
          this.$set(
            this.basicFormModel,
            "businessLicense",
            this.businessLicenseList[0]?.url
          );
        } else {
          this.$set(this.basicFormModel, "businessLicense", undefined);
        }
        this.$nextTick(() => {
          this.$refs.basic.validate();
        });
      },
      deep: true,
      immediate: true,
    },
    frontPicList: {
      handler(val) {
        if (val.length > 0 && this.reversePicList.length > 0) {
          this.$set(this.basicFormModel, "idCardPhotoUrl", "url");
        } else {
          this.$set(this.basicFormModel, "idCardPhotoUrl", undefined);
        }
        this.$nextTick(() => {
          this.$refs.basic.validate();
        });
      },
      deep: true,
      immediate: true,
    },
    reversePicList: {
      handler(val) {
        if (val.length > 0 && this.frontPicList.length > 0) {
          this.$set(this.basicFormModel, "idCardPhotoUrl", "url");
        } else {
          this.$set(this.basicFormModel, "idCardPhotoUrl", undefined);
        }
        this.$nextTick(() => {
          this.$refs.basic.validate();
        });
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.getAllDictList();
    this.action = this.$route.query.action;
    console.log(this.$route.query, 222);
    if (this.action === "edit" || !this.action) {
      this.getNowState();
    }
    if (this.action === "audit" || this.action === "view") {
      this.queryDetail();
    }
  },
  methods: {
    // 处理企业前缀输入，只允许正整数
    handleEntPrefixInput(value) {
      // 移除所有非数字字符
      const numericValue = value.replace(/[^0-9]/g, "");
      // 移除前导零，但保留单个0
      // const cleanValue =
      //   numericValue.replace(/^0+/, "") || (numericValue === "0" ? "0" : "");
      // 如果值被修改了，更新模型
      if (numericValue !== value) {
        this.$set(this.basicFormModel, "entPrefix", numericValue);
      }
    },
    // 处理企业前缀按键输入，阻止非数字字符
    handleEntPrefixKeypress(event) {
      // 允许的按键：数字0-9
      const allowedKeys = /[0-9]/;
      // 控制键：退格、删除、Tab、Enter、箭头键等
      const controlKeys = [
        "Backspace",
        "Delete",
        "Tab",
        "Enter",
        "ArrowLeft",
        "ArrowRight",
        "ArrowUp",
        "ArrowDown",
      ];

      // 如果不是数字且不是控制键，阻止输入
      if (!allowedKeys.test(event.key) && !controlKeys.includes(event.key)) {
        event.preventDefault();
      }
    },
    // 从审核页面过来 查详情
    queryDetail() {
      let params = {};
      if (this.$route.query.userView) {
        params = { entCode: this.$route.query.entCode };
      } else {
        params = { id: this.$route.query.id };
      }
      accApi.queryCompanyById(params).then((res) => {
        this.disabled = true;
        this.basicFormModel = res.data;
        this.businessLicenseList = [{ url: res.data.businessLicense }];
        this.frontPicList = [{ url: res.data.idCardPhotoUrl1 }];
        this.reversePicList = [{ url: res.data.idCardPhotoUrl2 }];
        this.checked = !this.basicFormModel.businessEndDate;
        this.$set(
          this.basicFormModel,
          "entPrefix",
          this.basicFormModel.entPrefix.slice(7)
        );
        this.$set(
          this.basicFormModel,
          "role",
          JSON.parse(this.basicFormModel.role)
        );
        this.$set(this.basicFormModel, "industry", [
          this.basicFormModel.industry1,
          this.basicFormModel.industry2,
        ]);
        this.$set(
          this.basicFormModel,
          "registeredAddress",
          [
            this.basicFormModel.registeredAddress1,
            this.basicFormModel.registeredAddress2,
            this.basicFormModel.registeredAddress3,
          ].filter((item) => item)
        );
        this.$set(this.form, "auditMessage", this.basicFormModel.auditDesc);
        this.$nextTick(() => {
          this.$refs.basic.validate();
        });
      });
    },

    getAllDictList() {
      // 省市区
      accApi.comApi({ type: "PROVINCE" }).then((res) => {
        this.areaList = this.convertToCascade(res.data, res.data["86"], 1);
      });
      // 入驻角色
      accApi.getEntRoleList().then((res) => {
        this.entRoleList = res.data.map((item) => {
          return {
            label: item.roleName,
            value: item.roleCode,
          };
        });
      });
      // 企业主体
      accApi
        .dictionaryApi({ dictTypeId: "1784414823669301251" })
        .then((res) => {
          this.enterpriseDominantList = res.data.map((item) => {
            return {
              label: item.name,
              value: item.code,
            };
          });
        });
      // // 法人证件类型
      // accApi.comApi({ type: 'SUBJECT_CLASSIFICATION' }).then(res => {
      //   this.cardList = res.data.map(item => {
      //     return {
      //       label: item.name,
      //       value: item.id
      //     }
      //   })
      // })
      // 法人证件类型
      accApi.comApi({ type: "CERTIFICATE_TYPE" }).then((res) => {
        this.cardList = res.data.map((item, i) => {
          return {
            label: item,
            value: i.toString(),
          };
        });
      });
      // 行业大类
      accApi.comApi({ type: "INDUSTRY_CATEGORY" }).then((res) => {
        this.workTypeList = this.convertToCascade(res.data, res.data["all"]);
      });
      //
    },
    // 转换为省市区级联格式
    convertToCascade(data, arr, type) {
      const cascadeData = [];
      const provinces = arr;

      for (const provinceCode in provinces) {
        const provinceInfo = {
          value: provinceCode,
          label: provinces[provinceCode],
        };

        // 获取城市列表
        if (data[provinceCode]) {
          const cities = data[provinceCode];
          const cityChildren = [];

          for (const cityCode in cities) {
            const cityInfo = {
              value: cityCode,
              label: cities[cityCode],
            };

            // 获取区县列表
            if (data[cityCode] && type) {
              const districts = data[cityCode];
              const districtChildren = [];

              for (const districtCode in districts) {
                districtChildren.push({
                  value: districtCode,
                  label: districts[districtCode],
                });
              }

              // 只有当有区县数据时才添加 children
              if (districtChildren.length > 0) {
                cityInfo.children = districtChildren;
              }
            }

            cityChildren.push(cityInfo);
          }

          // 只有当有城市数据时才添加 children
          if (cityChildren.length > 0) {
            provinceInfo.children = cityChildren;
          }
        }

        cascadeData.push(provinceInfo);
      }

      return cascadeData;
    },
    getNowState() {
      accApi.getCompanyInfoApi().then((res) => {
        console.log(res);
        if (res.data) {
          if (res.data.auditStatus === "0") {
            this.action = "companyInfoEditApi";
            this.title = "已保存";
            this.description = "已保存为草稿。。。";
            this.alertType = "success";
          }
          if (res.data.auditStatus === "1") {
            this.action = "back";
            this.title = "已提交";
            this.description = "当前申请已提交，正在审核中。。。";
            this.alertType = "warning";
          }
          if (res.data.auditStatus === "2") {
            // this.action = 'companyInfoEditApi'
            this.action = "success";
            this.title = res.data.auditDesc
              ? `已通过 :${res.data.auditDesc}`
              : "已通过";
            this.description = "当前申请审核通过";
            this.alertType = "success";
          }
          if (res.data.auditStatus === "3") {
            this.action = "companyInfoEditApi";
            this.title = `已驳回 :${res.data.auditDesc}`;
            this.description = "当前申请已驳回,可编辑重新提交审核";
            this.alertType = "error";
          }
          this.disabled = true;
          this.basicFormModel = res.data;
          this.businessLicenseList = [{ url: res.data.businessLicense }];
          this.frontPicList = [{ url: res.data.idCardPhotoUrl1 }];
          this.reversePicList = [{ url: res.data.idCardPhotoUrl2 }];
          this.checked = !this.basicFormModel.businessEndDate;
          this.$set(
            this.basicFormModel,
            "role",
            JSON.parse(this.basicFormModel.role)
          );
          this.$set(this.basicFormModel, "industry", [
            this.basicFormModel.industry1,
            this.basicFormModel.industry2,
          ]);
          this.$set(
            this.basicFormModel,
            "entPrefix",
            this.basicFormModel.entPrefix.slice(7)
          );
          this.$set(
            this.basicFormModel,
            "registeredAddress",
            [
              this.basicFormModel.registeredAddress1,
              this.basicFormModel.registeredAddress2,
              this.basicFormModel.registeredAddress3,
            ].filter((item) => item)
          );
        } else {
          this.action = "companyInfoAddApi";
          this.disabled = false;
        }
      });
    },
    handleChange() {
      this.disabled = !this.disabled;
      if (this.disabled) {
        this.getNowState();
      }
    },
    addAndEditFrom(type) {
      this.$refs.basic.validate((valid) => {
        if (valid) {
          const api = type === 1 ? "companyInfoSubmitApi" : this.action;
          accApi[api]({
            ...this.basicFormModel,
            role: JSON.stringify(this.basicFormModel.role),
            industry1: this.basicFormModel.industry[0],
            industry2: this.basicFormModel.industry[1],
            registeredAddress1: this.basicFormModel.registeredAddress[0],
            registeredAddress2: this.basicFormModel.registeredAddress[1] || "",
            registeredAddress3: this.basicFormModel.registeredAddress[2] || "",
            businessLicense: this.businessLicenseList[0]?.url,
            idCardPhotoUrl1: this.frontPicList[0]?.url,
            idCardPhotoUrl2: this.reversePicList[0]?.url,
            entPrefix: this.basicFormModel.entPrefix
              ? "88.588." + this.basicFormModel.entPrefix
              : "",
          }).then((res) => {
            this.$message.success(res.message);
            this.getNowState();
          });
        }
      });
    },
    submit() {
      this.$confirm("请确认是提交审核?", "警告").then(() => {
        acc.companyInfoSubmitApi({ id: this.basicFormModel.id }).then(() => {
          this.$message.success("提交成功!");
          this.getNowState();
        });
      });
    },
    backup() {
      this.$confirm("请确认是否撤销审核?", "警告").then(() => {
        acc.companyInfoUnSubmitApi({ id: this.basicFormModel.id }).then(() => {
          this.$message.success("撤销成功!");
          this.getNowState();
        });
      });
    },
    // 审核通过
    auditYes() {
      console.log(
        this.basicFormModel.entPrefix,
        "====================entPrefix"
      );

      this.$refs.basic.validate((valid) => {
        if (valid) {
          this.$confirm("请确认是否审核通过?", "警告").then(() => {
            acc
              .companyAuditPass({
                entPrefix: this.basicFormModel.entPrefix
                  ? "88.588." + this.basicFormModel.entPrefix
                  : "",
                auditDesc: this.form.auditMessage,
                id: this.basicFormModel.id,
              })
              .then(() => {
                this.$message.success("操作成功!");
                this.returnBack();
              });
          });
        }
      });
    },
    // 审核拒绝
    auditNo() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm("请确认是否驳回?", "警告").then(() => {
            acc
              .companyAuditReject({
                id: this.basicFormModel.id,
                auditDesc: this.form.auditMessage,
                auditStatus: "1",
                account: this.basicFormModel.account,
              })
              .then(() => {
                this.$message.success("驳回成功!");
                this.returnBack();
              });
          });
        } else {
          this.$refs.targetElement.scrollIntoView({ behavior: "smooth" });
        }
      });
    },
    returnBack() {
      if (this.$route.query.userView) {
        this.$router.replace({ path: "companyAccount" });
      } else {
        this.$router.replace({ path: "enterpriseAudit" });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.el-select--small,
.el-cascader--small,
.el-input-number--small {
  width: 100%;
}

.button-container {
  line-height: 60px;
  text-align: center;
}
</style>
